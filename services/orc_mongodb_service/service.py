#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC MongoDB服务 - 重构版

使用共享模块架构的ORC到MongoDB用户数据处理服务：
- 读取ORC文件中的用户数据
- 处理和验证用户PID数据
- 批量写入MongoDB数据库
- 支持多进程并行处理
- 统一的配置、日志、异常处理

作者: User-DF Team
版本: 2.0.0
"""

import os
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from shared.core import Config<PERSON>ana<PERSON>, Logger, ExceptionHandler
from shared.database.mongodb import MongoDBPool, MongoDBOperations
from shared.utils import DataProcessor, TimeUtils
from services.orc_mongodb_service.processors import ORCDataProcessor
from services.orc_mongodb_service.orc_reader import SimpleORCReader


@dataclass
class ServiceConfig:
    """服务配置"""
    # ORC文件配置
    orc_base_path: str
    orc_file_pattern: str

    # MongoDB配置
    mongodb_collection: str

    # 处理配置
    max_pids_per_user: int

    # 省份配置
    province_ids: List[int]

    # 监控配置
    progress_report_interval: int
    stats_output_interval: int

    # 列名映射配置
    column_mapping: Dict[str, List[str]]

    # 日期配置
    process_date: Optional[str] = None
    date_range: Optional[Tuple[str, str]] = None

    # 批处理配置（简化配置）
    batch_size: int = 1000
    pid_query_batch_size: int = 1000
    enable_batch_optimization: bool = True

    # Milvus配置
    enable_milvus_filtering: bool = True

    # 测试模式配置
    test_mode: bool = False
    mock_milvus: Optional[Dict[str, Any]] = None


@dataclass
class ProcessingStats:
    """处理统计"""
    total_files: int = 0
    processed_files: int = 0
    total_records: int = 0
    processed_records: int = 0
    valid_users: int = 0
    invalid_users: int = 0
    skipped_users: int = 0  # 跳过的用户数
    mongodb_inserts: int = 0
    mongodb_updates: int = 0
    errors: int = 0
    start_time: float = 0.0
    end_time: float = 0.0
    
    @property
    def processing_time(self) -> float:
        """处理时间"""
        if self.end_time > 0:
            return self.end_time - self.start_time
        return time.time() - self.start_time
    
    @property
    def records_per_second(self) -> float:
        """每秒处理记录数"""
        if self.processing_time > 0:
            return self.processed_records / self.processing_time
        return 0.0


class ORCMongoDBService:
    """ORC MongoDB服务"""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化服务
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager or ConfigManager()
        self.logger = Logger.get_logger("ORCMongoDBService")
        
        # 加载配置
        self._load_config()
        
        # 初始化组件
        self.mongodb_pool = MongoDBPool("mongodb", config_manager=self.config_manager)
        self.orc_reader = SimpleORCReader("ORCMongoDBService")
        self.data_processor = DataProcessor()
        self.orc_processor = ORCDataProcessor(
            self.data_processor,
            self.config,
            self.config_manager
        )
        
        # 统计信息
        self.stats = ProcessingStats()
        
        # 关闭回调函数
        self.shutdown_callback = None
        
        self.logger.info("ORC MongoDB服务初始化完成")
    
    def set_shutdown_callback(self, callback):
        """
        设置关闭回调函数
        
        Args:
            callback: 返回是否应该关闭的回调函数
        """
        self.shutdown_callback = callback
        
        # 同时传递给ORC处理器
        if hasattr(self, 'orc_processor') and self.orc_processor:
            self.orc_processor.set_shutdown_callback(callback)
    
    def should_shutdown(self) -> bool:
        """
        检查是否应该关闭服务
        
        Returns:
            是否应该关闭
        """
        if self.shutdown_callback:
            return self.shutdown_callback()
        return False
    
    def _load_config(self):
        """加载服务配置"""
        try:
            # 加载服务特定配置（包含所有配置项）
            service_config = self.config_manager.get_config("orc_mongodb_service", default={})

            # 从服务配置中提取MongoDB配置
            mongodb_config = service_config.get("mongodb", {})

            # 获取批处理配置
            batch_config = service_config.get("batch_processing", {})

            # 构建服务配置
            self.config = ServiceConfig(
                # ORC文件配置
                orc_base_path=service_config.get("orc_base_path", "/data/hive_data"),
                orc_file_pattern=service_config.get("orc_file_pattern", "*.orc"),

                # MongoDB配置
                mongodb_collection=mongodb_config.get("collections", {}).get("user_pid_collection", "user_pid_records_optimized"),

                # 处理配置
                max_pids_per_user=service_config.get("max_pids_per_user", 300),

                # 省份配置
                province_ids=service_config.get("province_ids", [100]),

                # 监控配置
                progress_report_interval=service_config.get("progress_report_interval", 30),
                stats_output_interval=service_config.get("stats_output_interval", 60),

                # 列名映射配置
                column_mapping=service_config.get("column_mapping", {
                    "uid_columns": ["uid", "user_id", "userid", "UID", "USER_ID", "id"],
                    "pid_columns": ["pic_id_list", "pid_list", "pid", "product_id", "item_id", "PID", "PRODUCT_ID"]
                }),

                # 日期配置
                process_date=service_config.get("process_date"),
                date_range=service_config.get("date_range"),

                # 批处理配置（简化配置）
                batch_size=batch_config.get("batch_size", 1000),
                pid_query_batch_size=batch_config.get("pid_query_batch_size", 1000),
                enable_batch_optimization=batch_config.get("enable_batch_optimization", True),

                # Milvus配置
                enable_milvus_filtering=service_config.get("enable_milvus_filtering", True),

                # 测试模式配置
                test_mode=service_config.get("test_mode", False),
                mock_milvus=service_config.get("mock_milvus", {})
            )
            
            self.logger.info("服务配置加载完成")
            self.logger.debug(f"ORC基础路径: {self.config.orc_base_path}")
            self.logger.debug(f"省份列表: {self.config.province_ids}")
            self.logger.debug(f"批次大小: {self.config.batch_size}")
            self.logger.debug(f"PID查询批次大小: {self.config.pid_query_batch_size}")
            self.logger.debug(f"批量优化启用: {self.config.enable_batch_optimization}")
            
        except Exception as e:
            self.logger.error(f"加载服务配置失败: {e}")
            raise
    
    def process_date_range(self, start_date: str, end_date: str) -> bool:
        """
        处理日期范围内的数据
        
        Args:
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            
        Returns:
            处理是否成功
        """
        try:
            self.logger.info(f"开始处理日期范围: {start_date} - {end_date}")
            
            # 获取日期列表
            date_list = TimeUtils.get_date_range(start_date, end_date)
            
            total_success = True
            for process_date in date_list:
                # 检查是否需要关闭服务
                if self.should_shutdown():
                    self.logger.info("接收到关闭信号，停止处理日期范围")
                    return False
                
                date_str = TimeUtils.format_datetime(
                    datetime.combine(process_date, datetime.min.time()),
                    'compact_date'
                )
                
                success = self.process_single_date(date_str)
                if not success:
                    total_success = False
                    self.logger.error(f"处理日期 {date_str} 失败")
            
            return total_success
            
        except Exception as e:
            self.logger.error(f"处理日期范围失败: {e}")
            ExceptionHandler.handle_exception(e, {"start_date": start_date, "end_date": end_date})
            return False
    
    def process_single_date(self, process_date: str) -> bool:
        """
        处理单个日期的数据
        
        Args:
            process_date: 处理日期 (YYYYMMDD)
            
        Returns:
            处理是否成功
        """
        try:
            self.logger.info(f"开始处理日期: {process_date}")
            
            # 重置统计
            self.stats = ProcessingStats()
            self.stats.start_time = time.time()

            # 处理所有省份的数据
            all_orc_files = []

            for province_id in self.config.province_ids:
                # 构建分区路径: hive_data/prov_id=100/statis_ymd=20250608
                partition_path = os.path.join(
                    self.config.orc_base_path,
                    f"prov_id={province_id}",
                    f"statis_ymd={process_date}"
                )

                if not os.path.exists(partition_path):
                    self.logger.warning(f"分区路径不存在: {partition_path}")
                    continue

                # 查找ORC文件
                orc_files = self.orc_reader.list_orc_files(
                    partition_path,
                    pattern=self.config.orc_file_pattern,
                    recursive=False
                )

                if orc_files:
                    all_orc_files.extend(orc_files)
                    self.logger.info(f"省份 {province_id} 找到 {len(orc_files)} 个ORC文件")
                else:
                    self.logger.warning(f"省份 {province_id} 分区中没有找到ORC文件: {partition_path}")

            if not all_orc_files:
                self.logger.warning(f"所有省份分区中都没有找到ORC文件")
                return False

            self.stats.total_files = len(all_orc_files)
            self.logger.info(f"总共找到 {len(all_orc_files)} 个ORC文件")

            # 单进程处理文件
            success = self._process_files_single_process(all_orc_files, process_date)
            
            # 更新结束时间
            self.stats.end_time = time.time()
            
            # 输出最终统计
            self._output_final_stats(process_date)
            
            return success
            
        except Exception as e:
            self.logger.error(f"处理单个日期失败: {process_date}, {e}")
            ExceptionHandler.handle_exception(e, {"process_date": process_date})
            return False
    
    def _process_files_single_process(self, orc_files: List[str], process_date: str) -> bool:
        """
        单进程处理文件
        
        Args:
            orc_files: ORC文件列表
            process_date: 处理日期
            
        Returns:
            处理是否成功
        """
        try:
            mongodb_ops = MongoDBOperations(self.mongodb_pool, self.config.mongodb_collection)
            
            for i, orc_file in enumerate(orc_files):
                # 检查是否需要关闭服务
                if self.should_shutdown():
                    self.logger.info("接收到关闭信号，停止处理文件")
                    return False
                
                self.logger.info(f"处理文件 {i+1}/{len(orc_files)}: {orc_file}")
                
                # 处理单个文件
                file_success = self._process_single_file(orc_file, process_date, mongodb_ops)
                
                if file_success:
                    self.stats.processed_files += 1
                else:
                    self.stats.errors += 1
                
                # 定期输出进度（包含更多监控信息）
                if (i + 1) % 10 == 0:
                    self._output_enhanced_progress_stats(i + 1, len(orc_files))
            
            # 只要处理了至少一个文件成功，就认为整体成功
            # 允许部分文件失败，但不能全部失败
            if self.stats.processed_files == 0 and self.stats.errors > 0:
                self.logger.error(f"所有文件处理都失败了，成功: {self.stats.processed_files}, 失败: {self.stats.errors}")
                return False
            elif self.stats.errors > 0:
                self.logger.warning(f"部分文件处理失败，成功: {self.stats.processed_files}, 失败: {self.stats.errors}")
                return True
            else:
                self.logger.info(f"所有文件处理成功，成功: {self.stats.processed_files}, 失败: {self.stats.errors}")
                return True
            
        except Exception as e:
            self.logger.error(f"单进程处理文件失败: {e}")
            return False
    

    
    def _process_single_file(self, orc_file: str, process_date: str,
                           mongodb_ops: MongoDBOperations) -> bool:
        """
        处理单个ORC文件（支持分批处理）

        Args:
            orc_file: ORC文件路径
            process_date: 处理日期
            mongodb_ops: MongoDB操作对象

        Returns:
            处理是否成功
        """
        try:
            # 从文件路径提取prov_id
            prov_id = self._extract_prov_id_from_path(orc_file)

            # 使用ORC处理器处理文件（现在返回的是分批处理后的结果）
            user_data_list = self.orc_processor.process_orc_file_with_mongodb_write(
                orc_file, process_date, prov_id, mongodb_ops
            )

            # 同步ORC处理器的统计信息到服务统计
            self._sync_orc_processor_stats()

            # 文件处理完成的详细信息已在处理器中输出，这里只记录debug信息
            self.logger.debug(f"文件处理完成: {orc_file}, 总处理用户数: {len(user_data_list)}, prov_id: {prov_id}")
            return True

        except Exception as e:
            self.logger.error(f"处理单个文件失败: {orc_file}, {e}")
            return False

    def _sync_orc_processor_stats(self):
        """同步ORC处理器的统计信息到服务统计"""
        try:
            orc_stats = self.orc_processor.get_stats()

            # 直接同步累计统计（处理器已经维护了全局累计统计）
            if "effective_users" in orc_stats:
                self.stats.valid_users = orc_stats["effective_users"]

            # 同步处理记录数
            if "valid_records" in orc_stats:
                self.stats.processed_records = orc_stats["valid_records"]

            # 同步无效用户数（经过处理后没有有效PID的用户数）
            if "invalid_users" in orc_stats:
                self.stats.invalid_users = orc_stats["invalid_users"]

            # 同步跳过用户数
            if "skipped_users" in orc_stats:
                self.stats.skipped_users = orc_stats["skipped_users"]

            # 同步MongoDB操作统计
            if "mongodb_inserts" in orc_stats:
                self.stats.mongodb_inserts = orc_stats["mongodb_inserts"]

            if "mongodb_updates" in orc_stats:
                self.stats.mongodb_updates = orc_stats["mongodb_updates"]

        except Exception as e:
            self.logger.warning(f"同步ORC处理器统计信息失败: {e}")

    def _batch_write_to_mongodb(self, user_data_list: List[Dict[str, Any]],
                               mongodb_ops: MongoDBOperations) -> bool:
        """
        批量写入MongoDB，支持时间戳验证和is_stored字段更新
        
        Args:
            user_data_list: 用户数据列表
            mongodb_ops: MongoDB操作对象
            
        Returns:
            写入是否成功
        """
        try:
            batch_size = self.config.batch_size
            total_users = len(user_data_list)
            
            for i in range(0, total_users, batch_size):
                # 检查是否需要关闭服务
                if self.should_shutdown():
                    self.logger.info("接收到关闭信号，停止批量写入操作")
                    return False
                batch_data = user_data_list[i:i + batch_size]
                
                # 先检查现有数据的时间戳，过滤掉不需要更新的数据
                valid_updates = self._filter_by_timestamp(batch_data, mongodb_ops)
                
                if not valid_updates:
                    self.logger.debug(f"批次中所有数据都被时间戳验证跳过")
                    continue
                
                # 构建批量操作
                operations = []
                for user_data in valid_updates:
                    # 在构建每个操作前检查是否需要关闭服务
                    if self.should_shutdown():
                        self.logger.info("接收到关闭信号，停止构建批量操作")
                        return False

                    # 准备更新数据，确保is_stored设置为false
                    update_data = user_data.copy()
                    if "vector_status" in update_data:
                        update_data["vector_status"]["is_stored"] = False
                        update_data["vector_status"]["stored_at_days"] = None
                    else:
                        update_data["vector_status"] = {
                            "is_stored": False,
                            "stored_at_days": None
                        }

                    # 构建智能更新操作（处理pid_groups覆盖逻辑）
                    filter_dict = {"_id": user_data["uid"]}
                    update_dict = self._build_smart_update_operation(update_data)

                    from pymongo import UpdateOne
                    operations.append(UpdateOne(filter_dict, update_dict, upsert=True))
                
                # 执行批量操作（使用配置的写入优化参数）
                result = mongodb_ops.bulk_write(operations)
                
                # 更新统计信息（无论是否完全成功）
                # upsert操作中，新插入的文档计入upserted_count而不是inserted_count
                self.stats.mongodb_inserts += result.inserted_count + result.upserted_count
                self.stats.mongodb_updates += result.modified_count
                
                # 记录跳过的文档数量（由于时间戳验证）
                skipped_count = len(batch_data) - len(valid_updates)
                if skipped_count > 0:
                    self.logger.debug(f"跳过 {skipped_count} 个文档（时间戳验证失败）")
                
                # 处理批量写入结果
                if result.success:
                    self.logger.debug(f"批量写入成功: 插入/更新 {result.inserted_count + result.upserted_count + result.modified_count} 个文档")
                else:
                    # 即使有错误，只要有部分成功就继续处理
                    total_processed = result.inserted_count + result.upserted_count + result.modified_count
                    if total_processed > 0:
                        self.logger.warning(f"批量写入部分成功: 成功处理 {total_processed} 个文档，错误信息: {result.error_message}")
                    else:
                        self.logger.error(f"批量写入完全失败: {result.error_message}")
                        return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"批量写入MongoDB失败: {e}")
            return False
    
    def _filter_by_timestamp(self, user_data_list: List[Dict[str, Any]], 
                           mongodb_ops: MongoDBOperations) -> List[Dict[str, Any]]:
        """
        根据时间戳过滤需要更新的用户数据
        
        Args:
            user_data_list: 用户数据列表
            mongodb_ops: MongoDB操作对象
            
        Returns:
            需要更新的用户数据列表
        """
        try:
            if not user_data_list:
                return []
            
            # 提取所有uid
            uids = [user_data["uid"] for user_data in user_data_list]

            # 查询现有数据的updated_days（使用_id查询）
            query_filter = {"_id": {"$in": uids}}

            # 创建查询选项
            from shared.database.mongodb.operations import QueryOptions
            query_options = QueryOptions(projection={"_id": 1, "updated_days": 1})

            existing_data = {}
            try:
                # 使用find_many方法查询文档
                for doc in mongodb_ops.find_many(query_filter, query_options):
                    uid = doc.get("_id")
                    updated_days = doc.get("updated_days")
                    if uid is not None:
                        existing_data[uid] = updated_days
            except Exception as query_error:
                self.logger.warning(f"查询现有用户数据失败: {query_error}")
                # 如果查询失败，继续处理但不过滤数据
                pass
            
            # 过滤数据：只保留新数据时间戳大于等于现有数据时间戳的记录
            valid_updates = []
            skipped_count = 0
            
            for user_data in user_data_list:
                # 在处理每个用户数据前检查是否需要关闭服务
                if self.should_shutdown():
                    self.logger.info("接收到关闭信号，停止时间戳过滤")
                    return valid_updates  # 返回已处理的部分数据

                uid = user_data["uid"]
                new_updated_days = user_data["updated_days"]

                existing_updated_days = existing_data.get(uid)

                # 如果文档不存在，或者新数据的时间戳大于现有数据的时间戳，则允许更新
                # 如果当前数据更新时间小于等于更新时间，则对用户进行跳过
                if existing_updated_days is None or new_updated_days > existing_updated_days:
                    valid_updates.append(user_data)
                else:
                    skipped_count += 1
                    self.logger.debug(
                        f"跳过用户 {uid}：新时间戳 {new_updated_days} <= 现有时间戳 {existing_updated_days}"
                    )
            
            # 更新跳过用户统计
            self.stats.skipped_users += skipped_count
            
            # 显示跳过用户数目统计
            if skipped_count > 0:
                self.logger.info(
                    f"时间戳过滤完成：保留 {len(valid_updates)} 个用户，跳过 {skipped_count} 个用户"
                )
            else:
                self.logger.info(f"时间戳过滤完成：保留 {len(valid_updates)} 个用户，无跳过用户")
            
            return valid_updates
            
        except Exception as e:
            self.logger.error(f"时间戳过滤失败: {e}")
            # 如果过滤失败，返回原始数据以保证服务可用性
            return user_data_list

    def _batch_write_to_mongodb_chunked(self, user_data_list: List[Dict[str, Any]],
                                       mongodb_ops: MongoDBOperations) -> bool:
        """
        分批写入MongoDB（进一步优化内存使用）

        Args:
            user_data_list: 用户数据列表
            mongodb_ops: MongoDB操作对象

        Returns:
            写入是否成功
        """
        try:
            batch_size = self.config.batch_size
            total_users = len(user_data_list)

            self.logger.info(f"开始分批写入MongoDB，总用户数: {total_users}, 批次大小: {batch_size}")

            success_count = 0
            for i in range(0, total_users, batch_size):
                # 检查是否需要关闭服务
                if self.should_shutdown():
                    self.logger.info("接收到关闭信号，停止分批写入操作")
                    return success_count > 0  # 如果有部分成功，返回True

                batch_data = user_data_list[i:i + batch_size]
                batch_num = i // batch_size + 1
                total_batches = (total_users + batch_size - 1) // batch_size

                self.logger.debug(f"处理写入批次 {batch_num}/{total_batches}, 用户数: {len(batch_data)}")

                # 先检查现有数据的时间戳，过滤掉不需要更新的数据
                valid_updates = self._filter_by_timestamp(batch_data, mongodb_ops)

                if not valid_updates:
                    self.logger.debug(f"批次 {batch_num} 中所有数据都被时间戳验证跳过")
                    continue

                # 构建批量操作
                operations = []
                for user_data in valid_updates:
                    # 在构建每个操作前检查是否需要关闭服务
                    if self.should_shutdown():
                        self.logger.info("接收到关闭信号，停止构建批量操作")
                        return success_count > 0

                    # 准备更新数据，确保is_stored设置为false
                    update_data = user_data.copy()
                    if "vector_status" in update_data:
                        update_data["vector_status"]["is_stored"] = False
                        update_data["vector_status"]["stored_at_days"] = None
                    else:
                        update_data["vector_status"] = {
                            "is_stored": False,
                            "stored_at_days": None
                        }

                    # 构建智能更新操作（处理pid_groups覆盖逻辑）
                    filter_dict = {"_id": user_data["uid"]}
                    update_dict = self._build_smart_update_operation(update_data)

                    from pymongo import UpdateOne
                    operations.append(UpdateOne(filter_dict, update_dict, upsert=True))

                # 执行批量操作（使用配置的写入优化参数）
                result = mongodb_ops.bulk_write(operations)

                # 更新统计信息
                self.stats.mongodb_inserts += result.inserted_count + result.upserted_count
                self.stats.mongodb_updates += result.modified_count

                # 记录跳过的文档数量
                skipped_count = len(batch_data) - len(valid_updates)
                if skipped_count > 0:
                    self.logger.debug(f"批次 {batch_num} 跳过 {skipped_count} 个文档（时间戳验证失败）")

                # 处理批量写入结果
                if result.success:
                    success_count += 1
                    processed_count = result.inserted_count + result.upserted_count + result.modified_count
                    self.logger.debug(f"批次 {batch_num} 写入成功: 处理 {processed_count} 个文档")
                else:
                    # 即使有错误，只要有部分成功就继续处理
                    total_processed = result.inserted_count + result.upserted_count + result.modified_count
                    if total_processed > 0:
                        success_count += 1
                        self.logger.warning(f"批次 {batch_num} 部分成功: 成功处理 {total_processed} 个文档，错误信息: {result.error_message}")
                    else:
                        self.logger.error(f"批次 {batch_num} 完全失败: {result.error_message}")

                # 定期输出进度
                if batch_num % 10 == 0:
                    self.logger.info(f"分批写入进度: {batch_num}/{total_batches} 批次完成")

            self.logger.info(f"分批写入完成，成功批次: {success_count}/{total_batches}")
            return success_count > 0

        except Exception as e:
            self.logger.error(f"分批写入MongoDB失败: {e}")
            return False


    
    def _extract_prov_id_from_path(self, file_path: str) -> Optional[str]:
        """
        从文件路径中提取prov_id
        
        Args:
            file_path: 文件路径
            
        Returns:
            提取的prov_id或None
        """
        try:
            # 文件路径格式: /data/hive_data/prov_id=100/statis_ymd=20250608/xxx.orc
            import re
            match = re.search(r'prov_id=([^/]+)', file_path)
            if match:
                prov_id = match.group(1)
                self.logger.debug(f"从路径提取prov_id: {prov_id}")
                return prov_id
            else:
                self.logger.warning(f"无法从路径提取prov_id: {file_path}")
                return None
        except Exception as e:
            self.logger.error(f"提取prov_id失败: {file_path}, {e}")
            return None
    
    def _output_progress_stats(self):
        """输出进度统计"""
        self.logger.info(
            f"处理进度 - 文件: {self.stats.processed_files}/{self.stats.total_files}, "
            f"记录: {self.stats.processed_records}, "
            f"有效用户: {self.stats.valid_users}, "
            f"速度: {self.stats.records_per_second:.1f} 记录/秒"
        )

    def _output_enhanced_progress_stats(self, current_file: int, total_files: int):
        """输出增强的进度统计（包含更多监控信息）"""
        elapsed_time = time.time() - self.stats.start_time
        progress_percent = (current_file / total_files) * 100

        # 计算文件处理速度（秒/文件）
        seconds_per_file = elapsed_time / current_file if current_file > 0 else 0

        # 估算剩余时间
        remaining_files = total_files - current_file
        eta_seconds = remaining_files * seconds_per_file if seconds_per_file > 0 else 0
        eta_minutes = eta_seconds / 60

        # 获取ORC处理器统计
        orc_stats = self.orc_processor.get_stats()
        milvus_queries = orc_stats.get('milvus_queries', 0)
        valid_pids = orc_stats.get('valid_pids_in_milvus', 0)

        # 计算用户有效率
        total_processed_users = self.stats.valid_users + self.stats.invalid_users
        user_valid_rate = (self.stats.valid_users / total_processed_users) * 100 if total_processed_users > 0 else 0

        # 格式化处理速度显示
        if seconds_per_file > 0:
            if seconds_per_file < 60:
                speed_display = f"{seconds_per_file:.1f}s/文件"
            else:
                minutes_per_file = seconds_per_file / 60
                speed_display = f"{minutes_per_file:.1f}分钟/文件"
        else:
            speed_display = "计算中..."

        # 格式化预计剩余时间显示
        if eta_minutes > 0:
            if eta_minutes < 60:
                eta_display = f"{eta_minutes:.0f}分钟"
            else:
                eta_hours = eta_minutes / 60
                eta_display = f"{eta_hours:.1f}小时"
        else:
            eta_display = "计算中..."

        self.logger.info(f"📊 进度: {current_file}/{total_files} ({progress_percent:.1f}%) | "
                        f"⏱️ 耗时: {elapsed_time:.0f}s | "
                        f"🚀 速度: {speed_display} | "
                        f"⏳ 预计剩余: {eta_display}")

        self.logger.info(f"👥 用户: {self.stats.valid_users}有效 ({user_valid_rate:.1f}%), {self.stats.invalid_users}无效, {self.stats.skipped_users}跳过 | "
                        f"🔍 Milvus: {milvus_queries}次查询, {valid_pids}个有效PID | "
                        f"💾 MongoDB: {self.stats.mongodb_inserts}插入, {self.stats.mongodb_updates}更新")
    
    def _output_final_stats(self, process_date: str):
        """输出最终统计"""
        self.logger.info(f"=== 处理完成统计 - {process_date} ===")
        self.logger.info(f"处理文件数: {self.stats.processed_files}/{self.stats.total_files}")
        if self.stats.errors > 0:
            self.logger.info(f"失败文件数: {self.stats.errors}")
        self.logger.info(f"处理记录数: {self.stats.processed_records}")
        self.logger.info(f"有效用户数: {self.stats.valid_users}")
        self.logger.info(f"无效用户数: {self.stats.invalid_users}")
        self.logger.info(f"跳过用户数: {self.stats.skipped_users}")
        self.logger.info(f"MongoDB插入: {self.stats.mongodb_inserts}")
        self.logger.info(f"MongoDB更新: {self.stats.mongodb_updates}")
        
        # 输出Milvus相关统计
        orc_stats = self.orc_processor.get_stats()
        if orc_stats.get('milvus_queries', 0) > 0:
            self.logger.info(f"Milvus查询次数: {orc_stats['milvus_queries']}")
            self.logger.info(f"Milvus找到PID数: {orc_stats['valid_pids_in_milvus']}")
            self.logger.info(f"Milvus内容向量总数: {orc_stats['milvus_total_content_vectors']}")
            self.logger.info(f"Milvus可查询内容向量数: {orc_stats['milvus_queryable_content_vectors']}")
            self.logger.info(f"Milvus内容向量查询成功率: {orc_stats['milvus_content_vector_query_rate']:.2%}")
            if orc_stats.get('milvus_retry_count', 0) > 0:
                self.logger.warning(f"Milvus重试次数: {orc_stats['milvus_retry_count']}")
            if orc_stats.get('milvus_error_users', 0) > 0:
                self.logger.error(f"Milvus错误用户数: {orc_stats['milvus_error_users']}")
        

        
        self.logger.info(f"处理时间: {self.stats.processing_time:.2f} 秒")
        self.logger.info(f"处理速度: {self.stats.records_per_second:.1f} 记录/秒")
        
        # 根据错误情况显示不同的完成状态
        if self.stats.errors == 0:
            self.logger.info("✓ 所有文件处理成功")
        elif self.stats.processed_files > 0:
            self.logger.warning(f"⚠ 部分文件处理成功 (成功: {self.stats.processed_files}, 失败: {self.stats.errors})")
        else:
            self.logger.error("✗ 所有文件处理失败")
            
        self.logger.info("=" * 50)
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查MongoDB连接
            mongodb_health = self.mongodb_pool.health_check()
            
            # 检查配置
            config_health = {
                "status": "healthy",
                "orc_base_path_exists": os.path.exists(self.config.orc_base_path)
            }
            
            # 获取ORC处理器的统计信息
            orc_stats = self.orc_processor.get_stats()
            
            return {
                "service": "ORCMongoDBService",
                "status": "healthy",
                "mongodb": mongodb_health,
                "config": config_health,
                "stats": {
                    "processed_files": self.stats.processed_files,
                    "processed_records": self.stats.processed_records,
                    "valid_users": self.stats.valid_users,
                    "mongodb_inserts": self.stats.mongodb_inserts,
                    "mongodb_updates": self.stats.mongodb_updates,
                    "milvus_queries": orc_stats.get("milvus_queries", 0),
                    "valid_pids_in_milvus": orc_stats.get("valid_pids_in_milvus", 0),
                    "milvus_total_content_vectors": orc_stats.get("milvus_total_content_vectors", 0),
                    "milvus_queryable_content_vectors": orc_stats.get("milvus_queryable_content_vectors", 0),
                    "milvus_content_vector_query_rate": orc_stats.get("milvus_content_vector_query_rate", 0.0),
                    "milvus_retry_count": orc_stats.get("milvus_retry_count", 0),
                    "milvus_error_users": orc_stats.get("milvus_error_users", 0)
                }
            }
            
        except Exception as e:
            return {
                "service": "ORCMongoDBService",
                "status": "unhealthy",
                "error": str(e)
            }

    def _build_smart_update_operation(self, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        构建智能更新操作，处理pid_groups的覆盖逻辑

        当存在相同timestamp_days时，新的pid_groups会覆盖旧的pid_groups

        Args:
            update_data: 要更新的数据

        Returns:
            MongoDB更新操作字典
        """
        # 如果没有pid_groups，使用简单的$set操作
        if "pid_groups" not in update_data:
            return {"$set": update_data}

        new_pid_groups = update_data["pid_groups"]

        # 构建更新操作
        update_operations = {}

        # 处理pid_groups的覆盖逻辑
        if new_pid_groups:
            # 创建timestamp_days到pid_groups的映射
            timestamp_to_groups = {}
            for group in new_pid_groups:
                timestamp_days = group.get("timestamp_days")
                if timestamp_days is not None:
                    timestamp_to_groups[timestamp_days] = group

            # 构建更新操作：先删除相同timestamp_days的组，然后添加新的组
            if timestamp_to_groups:
                # 删除具有相同timestamp_days的现有组
                update_operations["$pull"] = {
                    "pid_groups": {
                        "timestamp_days": {"$in": list(timestamp_to_groups.keys())}
                    }
                }

                # 添加新的pid_groups
                update_operations["$push"] = {
                    "pid_groups": {"$each": new_pid_groups}
                }

        # 处理其他字段的更新
        other_fields = {k: v for k, v in update_data.items() if k != "pid_groups"}
        if other_fields:
            if "$set" not in update_operations:
                update_operations["$set"] = {}
            update_operations["$set"].update(other_fields)

        # 如果没有构建任何操作，回退到简单的$set
        if not update_operations:
            return {"$set": update_data}

        return update_operations
