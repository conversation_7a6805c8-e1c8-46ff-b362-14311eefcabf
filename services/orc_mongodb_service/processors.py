#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC数据处理器

专门处理ORC文件中的用户数据：
- 读取和解析ORC文件
- 数据清洗和验证
- 用户数据聚合和优化
- PID去重和限制

作者: User-DF Team
版本: 2.0.0
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import pandas as pd
from collections import defaultdict
import random
import time

from shared.core import Logger, ConfigManager
from shared.utils import DataProcessor, TimeUtils
from shared.database.milvus import MilvusPool, MilvusVectorOperations
from services.orc_mongodb_service.orc_reader import SimpleORCReader, ReadOptions


class MockMilvusVectorOperations:
    """模拟的Milvus向量操作类，用于测试环境"""

    def __init__(self, config: Dict[str, Any], logger: Logger):
        """
        初始化Mock Milvus操作类

        Args:
            config: Mock <PERSON>lvus配置
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        self.query_success_rate = config.get('query_success_rate', 0.8)
        self.query_delay_ms = config.get('query_delay_ms', 10)
        self.use_random_seed = config.get('use_random_seed', True)
        self.shutdown_callback = None  # 添加关闭回调

        # 设置随机种子以获得可重复的结果
        if self.use_random_seed:
            random.seed(config.get('random_seed', 42))

        self.logger.info(f"Mock Milvus初始化完成，查询成功率: {self.query_success_rate}")

    def set_shutdown_callback(self, callback):
        """设置关闭回调函数"""
        self.shutdown_callback = callback

    def should_shutdown(self) -> bool:
        """检查是否应该关闭"""
        if self.shutdown_callback:
            return self.shutdown_callback()
        return False

    def query_vectors(self, ids: List[str], output_fields: Optional[List[str]] = None) -> Any:
        """
        模拟查询向量数据

        Args:
            ids: PID列表
            output_fields: 输出字段（在Mock中忽略）

        Returns:
            模拟的查询结果
        """
        # 模拟查询延迟，但支持中断
        if self.query_delay_ms > 0:
            delay_seconds = self.query_delay_ms / 1000.0
            # 将延迟分割成小段，以便及时响应关闭信号
            sleep_intervals = max(1, int(delay_seconds / 0.1))  # 每0.1秒检查一次
            for _ in range(sleep_intervals):
                if self.should_shutdown():
                    self.logger.info("接收到关闭信号，停止Mock Milvus查询")
                    # 返回空结果表示查询被中断
                    class MockQueryResult:
                        def __init__(self, success: bool, results: List[Dict[str, Any]]):
                            self.success = success
                            self.results = results
                    return MockQueryResult(success=False, results=[])
                time.sleep(0.1)

        # 根据成功率随机决定哪些PID存在
        existing_pids = []
        for pid in ids:
            if random.random() < self.query_success_rate:
                existing_pids.append(pid)

        # 构造模拟的查询结果
        mock_results = []
        for pid in existing_pids:
            mock_results.append({
                "id": pid,
                "vector": None  # 在PID存在性检查中不需要实际向量
            })

        # 创建模拟的查询结果对象
        class MockQueryResult:
            def __init__(self, success: bool, results: List[Dict[str, Any]]):
                self.success = success
                self.results = results

        self.logger.debug(f"Mock Milvus查询: {len(existing_pids)}/{len(ids)} 个PID存在")
        return MockQueryResult(success=True, results=mock_results)


@dataclass
class UserPIDData:
    """用户PID数据"""
    uid: int
    pid_list: List[str]
    pid_timestamps: Dict[str, int]  # PID -> timestamp mapping
    updated_days: int
    provid: Optional[str] = None
    is_stored: bool = False
    stored_at_days: Optional[int] = None


class ORCDataProcessor:
    """ORC数据处理器"""
    
    def __init__(self, data_processor: DataProcessor, config: Any, config_manager: Optional[ConfigManager] = None):
        """
        初始化ORC数据处理器

        Args:
            data_processor: 数据处理器
            config: 服务配置
            config_manager: 配置管理器
        """
        self.orc_reader = SimpleORCReader("ORCDataProcessor")
        self.data_processor = data_processor
        self.config = config
        self.config_manager = config_manager
        self.logger = Logger.get_logger("ORCDataProcessor")

        # 初始化Milvus连接
        self.milvus_pool = None
        self.content_ops = None
        self._init_milvus()

        # 处理统计
        self.stats = {
            "total_records": 0,
            "valid_records": 0,
            "invalid_records": 0,
            "effective_users": 0,
            "invalid_users": 0,  # 经过处理后没有有效PID的用户数
            "total_pids": 0,
            "milvus_queries": 0,
            "valid_pids_in_milvus": 0,
            "milvus_retry_count": 0,
            "milvus_error_users": 0,
            "milvus_total_content_vectors": 0,  # Milvus中内容向量总数
            "milvus_queryable_content_vectors": 0,  # 可查询的内容向量数
            "milvus_content_vector_query_rate": 0.0,  # 内容向量查询成功率
            "mongodb_inserts": 0,  # MongoDB插入数量
            "mongodb_updates": 0,  # MongoDB更新数量
            "skipped_users": 0  # 跳过的用户数量
        }
        
        # 当前处理的文件路径（用于统计）
        self.current_file_path = None
        
        # 关闭回调函数
        self.shutdown_callback = None
    
    def set_shutdown_callback(self, callback):
        """
        设置关闭回调函数

        Args:
            callback: 返回是否应该关闭的回调函数
        """
        self.shutdown_callback = callback

        # 同时传递给Mock Milvus（如果存在）
        if hasattr(self, 'content_ops') and self.content_ops and hasattr(self.content_ops, 'set_shutdown_callback'):
            self.content_ops.set_shutdown_callback(callback)
    
    def should_shutdown(self) -> bool:
        """
        检查是否应该关闭服务
        
        Returns:
            是否应该关闭
        """
        if self.shutdown_callback:
            return self.shutdown_callback()
        return False

    def _init_milvus(self):
        """初始化Milvus连接"""
        try:
            # 检查是否启用Milvus过滤
            if not getattr(self.config, 'enable_milvus_filtering', True):
                self.logger.info("Milvus过滤已禁用，跳过初始化")
                self.milvus_pool = None
                self.content_ops = None
                return

            # 检查是否启用测试模式和Mock Milvus
            test_mode = getattr(self.config, 'test_mode', False)
            mock_milvus_config = getattr(self.config, 'mock_milvus', {})
            mock_enabled = mock_milvus_config.get('enabled', False)

            if test_mode and mock_enabled:
                self.logger.info("测试模式已启用，使用Mock Milvus")
                self.milvus_pool = None
                self.content_ops = MockMilvusVectorOperations(mock_milvus_config, self.logger)
                return

            # 使用传入的配置管理器或从配置中获取Milvus配置
            if self.config_manager:
                milvus_config = self.config_manager.get_config("milvus", default={})
            else:
                # 如果没有配置管理器，尝试从config中获取
                milvus_config = getattr(self.config, 'milvus', None)

            if not milvus_config:
                self.logger.warning("未找到Milvus配置，跳过初始化")
                self.milvus_pool = None
                self.content_ops = None
                return

            # 记录Milvus配置信息用于调试
            self.logger.info(f"Milvus配置加载成功")
            connection_config = milvus_config.get("connection", {})
            self.logger.info(f"Milvus连接URI: {connection_config.get('uri', 'unknown')}")
            self.logger.info(f"Milvus数据库: {connection_config.get('database', 'default')}")

            # 创建Milvus连接池 - 修复参数传递方式
            self.milvus_pool = MilvusPool("milvus")
            if self.config_manager:
                self.milvus_pool.config_manager = self.config_manager

            # 创建内容向量操作对象
            collections_config = milvus_config.get("collections", {})
            content_collection = collections_config.get("content_collection", "content_tower_collection")
            self.logger.info(f"使用Milvus集合: {content_collection}")
            self.content_ops = MilvusVectorOperations(self.milvus_pool, content_collection)

            self.logger.info("Milvus连接初始化成功")

        except Exception as e:
            self.logger.warning(f"Milvus连接初始化失败: {e}")
            self.milvus_pool = None
            self.content_ops = None

    def _batch_query_pids_for_users(self, user_data_dict: Dict[int, UserPIDData], max_retries: int = 3) -> Dict[int, UserPIDData]:
        """
        批量查询多个用户的PID在Milvus中的存在性（优化版本）

        Args:
            user_data_dict: 用户数据字典
            max_retries: 最大重试次数

        Returns:
            更新后的用户数据字典，只包含在Milvus中存在的PID
        """
        if not user_data_dict:
            return user_data_dict

        # 检查是否启用批量PID查询优化
        if not getattr(self.config, 'enable_batch_optimization', True):
            self.logger.debug("批量PID查询优化已禁用，使用原有逐用户查询方式")
            return self._legacy_filter_users_pids(user_data_dict, max_retries)

        # 检查是否需要关闭服务
        if self.should_shutdown():
            self.logger.info("接收到关闭信号，停止批量PID查询")
            return user_data_dict

        # 如果Milvus不可用，返回原数据（不过滤）
        if not self.content_ops:
            self.logger.debug("Milvus不可用，跳过PID过滤")
            return user_data_dict

        # 收集所有用户的PID并去重
        all_pids = set()
        user_pid_mapping = {}  # uid -> set of pids

        for uid, user_data in user_data_dict.items():
            if user_data.pid_list:
                user_pids = set(user_data.pid_list)
                user_pid_mapping[uid] = user_pids
                all_pids.update(user_pids)

        if not all_pids:
            self.logger.debug("没有PID需要查询")
            return user_data_dict

        # 批量查询所有PID的存在性
        all_pids_list = list(all_pids)
        valid_pids_set = set(self._query_pids_in_milvus(all_pids_list, max_retries))

        # 为每个用户过滤有效的PID
        updated_user_data_dict = {}
        for uid, user_data in user_data_dict.items():
            if uid not in user_pid_mapping:
                # 用户没有PID，保持原样
                updated_user_data_dict[uid] = user_data
                continue

            # 过滤出在Milvus中存在的PID
            user_pids = user_pid_mapping[uid]
            valid_user_pids = user_pids.intersection(valid_pids_set)

            if not valid_user_pids:
                # 用户的所有PID都不存在，清空PID列表
                user_data.pid_list = []
                user_data.pid_timestamps = {}
                self.logger.debug(f"用户 {uid} 的所有PID在Milvus中都不存在")
            else:
                # 保留存在的PID，按原有顺序排列
                filtered_pids = [pid for pid in user_data.pid_list if pid in valid_user_pids]
                filtered_timestamps = {
                    pid: user_data.pid_timestamps[pid]
                    for pid in filtered_pids
                    if pid in user_data.pid_timestamps
                }

                user_data.pid_list = filtered_pids
                user_data.pid_timestamps = filtered_timestamps

                self.logger.debug(f"用户 {uid} PID过滤: {len(user_pids)} -> {len(filtered_pids)}")

            updated_user_data_dict[uid] = user_data

        # 统计最终有效用户数（仅在debug模式下输出）
        valid_users = sum(1 for user_data in updated_user_data_dict.values() if user_data.pid_list)
        self.logger.debug(f"批量PID查询完成，有效用户数: {valid_users}/{len(user_data_dict)}")

        return updated_user_data_dict

    def _legacy_filter_users_pids(self, user_data_dict: Dict[int, UserPIDData], max_retries: int = 3) -> Dict[int, UserPIDData]:
        """
        传统的逐用户PID过滤方式（向后兼容）

        Args:
            user_data_dict: 用户数据字典
            max_retries: 最大重试次数

        Returns:
            更新后的用户数据字典
        """
        updated_user_data_dict = {}

        for uid, user_data in user_data_dict.items():
            # 检查是否需要关闭服务
            if self.should_shutdown():
                self.logger.info("接收到关闭信号，停止传统PID过滤")
                break

            # 使用原有的单用户过滤方法
            filtered_user_data = self._filter_pids_by_milvus(user_data)
            updated_user_data_dict[uid] = filtered_user_data

        return updated_user_data_dict

    def _query_pids_in_milvus(self, pid_list: List[str], max_retries: int = 3) -> List[str]:
        """
        查询Milvus中存在的PID（带重试机制）

        Args:
            pid_list: PID列表
            max_retries: 最大重试次数

        Returns:
            在Milvus中存在的PID列表，如果Milvus不可用则返回原列表
        """
        # 如果PID列表为空，直接返回
        if not pid_list:
            return []

        # 检查是否需要关闭服务
        if self.should_shutdown():
            self.logger.info("接收到关闭信号，停止Milvus查询")
            return []

        # 如果Milvus不可用，返回原列表（不过滤）
        if not self.content_ops:
            # self.logger.debug("Milvus不可用，跳过PID过滤")
            return pid_list



        valid_pids = []
        batch_size = getattr(self.config, 'pid_query_batch_size', 1000)

        # 批量查询PID
        for i in range(0, len(pid_list), batch_size):
            # 在每个批次开始前检查是否需要关闭服务
            if self.should_shutdown():
                self.logger.info("接收到关闭信号，停止Milvus批量查询")
                break

            batch_pids = pid_list[i:i + batch_size]

            # 重试机制
            retry_count = 0
            success = False

            while retry_count <= max_retries and not success:
                # 在重试循环中也检查关闭信号
                if self.should_shutdown():
                    self.logger.info("接收到关闭信号，停止Milvus重试")
                    return valid_pids

                try:
                    # 查询向量是否存在
                    query_result = self.content_ops.query_vectors(
                        ids=batch_pids,
                        output_fields=["item_id"]  # 根据实际集合结构使用item_id字段
                    )

                    self.stats["milvus_queries"] += 1
                    # 记录查询的内容向量总数
                    self.stats["milvus_total_content_vectors"] += len(batch_pids)

                    # 详细的结果处理和日志记录
                    if query_result.success:
                        self.logger.debug(f"Milvus查询成功，查询了 {len(batch_pids)} 个PID")

                        if query_result.results:
                            self.logger.debug(f"查询结果类型: {type(query_result.results)}, 长度: {len(query_result.results)}")

                            # 处理不同的结果格式
                            for i, result in enumerate(query_result.results):
                                self.logger.debug(f"结果 {i}: 类型={type(result)}, 内容={result}")

                                pid = None
                                if isinstance(result, dict):
                                    # 字典格式：优先尝试常见的ID字段名
                                    pid = (result.get("id") or
                                          result.get("item_id") or
                                          result.get("pid") or
                                          result.get("_id"))
                                elif hasattr(result, 'id'):
                                    # 对象格式：result.id
                                    pid = result.id
                                elif hasattr(result, 'item_id'):
                                    # 对象格式：result.item_id
                                    pid = result.item_id
                                elif isinstance(result, (str, int)):
                                    # 直接是ID值
                                    pid = result
                                else:
                                    # 尝试其他可能的格式
                                    self.logger.warning(f"未知的结果格式: {type(result)}, 内容: {result}")
                                    continue

                                if pid:
                                    valid_pids.append(str(pid))
                                    self.stats["valid_pids_in_milvus"] += 1
                                    # 记录可查询的内容向量数
                                    self.stats["milvus_queryable_content_vectors"] += 1
                                    self.logger.debug(f"找到有效PID: {pid}")
                        else:
                            self.logger.debug("查询成功但没有返回结果")
                    else:
                        error_msg = query_result.error_message or "未知错误"
                        self.logger.warning(f"Milvus查询失败: {error_msg}")

                    success = True

                except Exception as e:
                    retry_count += 1
                    self.stats["milvus_retry_count"] += 1

                    if retry_count <= max_retries:
                        self.logger.warning(f"Milvus查询失败，第{retry_count}次重试: {e}")
                        # 简单的退避策略：等待递增的时间，但要支持中断
                        import time
                        sleep_time = retry_count * 0.5
                        # 将睡眠时间分割成小段，以便及时响应关闭信号
                        sleep_intervals = max(1, int(sleep_time / 0.1))  # 每0.1秒检查一次
                        for _ in range(sleep_intervals):
                            if self.should_shutdown():
                                self.logger.info("接收到关闭信号，停止重试等待")
                                return valid_pids
                            time.sleep(0.1)
                    else:
                        self.logger.error(f"Milvus查询失败，已达到最大重试次数({max_retries}): {e}")
                        # 记录错误用户数（这里是批次级别的错误）
                        self.stats["milvus_error_users"] += len(batch_pids)
                        # 重试失败后返回空列表，表示这批PID都无法查询
                        break



        # 计算内容向量查询成功率
        self._update_content_vector_query_rate()

        self.logger.debug(f"Milvus查询结果: {len(valid_pids)}/{len(pid_list)} 个PID存在")
        return valid_pids

    def _update_content_vector_query_rate(self) -> None:
        """更新内容向量查询成功率"""
        if self.stats["milvus_total_content_vectors"] > 0:
            self.stats["milvus_content_vector_query_rate"] = (
                self.stats["milvus_queryable_content_vectors"] / 
                self.stats["milvus_total_content_vectors"]
            )
        else:
            self.stats["milvus_content_vector_query_rate"] = 0.0

    def process_orc_file(self, orc_file_path: str, process_date: str, prov_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        处理ORC文件
        
        Args:
            orc_file_path: ORC文件路径
            process_date: 处理日期
            prov_id: 省份ID（从文件路径提取）
            
        Returns:
            处理后的用户数据列表
        """
        try:
            self.logger.info(f"开始处理ORC文件: {orc_file_path}")
            
            # 设置当前处理的文件路径
            self.current_file_path = orc_file_path
            
            # 重置统计（保留全局统计，只重置文件级别的统计）
            file_stats = {
                "total_records": 0,
                "valid_records": 0,
                "invalid_records": 0,
                "effective_users": 0,
                "invalid_users": 0,
                "total_pids": 0,
                "milvus_queries": 0,
                "valid_pids_in_milvus": 0,
                "milvus_retry_count": 0,
                "milvus_error_users": 0
            }
            
            # 保存之前的统计值
            prev_stats = self.stats.copy()
            
            # 更新统计结构
            for key in file_stats:
                if key in self.stats:
                    file_stats[key] = self.stats[key]
            
            # 使用分批处理（内存优化）
            user_data_list = self._process_orc_file_with_chunking(orc_file_path, process_date, prov_id)

            # 记录当前文件的有效用户数（累加到全局统计）
            current_file_effective_users = len(user_data_list)
            self.stats["effective_users"] += current_file_effective_users

            # 计算当前文件的处理统计
            current_file_stats = {
                key: self.stats[key] - prev_stats.get(key, 0)
                for key in file_stats if key in self.stats
            }

            # 修正当前文件的统计数据
            current_file_stats["effective_users"] = current_file_effective_users
            
            self.logger.info(
                f"ORC文件处理完成: {orc_file_path}, "
                f"总记录: {current_file_stats.get('total_records', 0)}, "
                f"有效记录: {current_file_stats.get('valid_records', 0)}, "
                f"有效用户: {current_file_stats.get('effective_users', 0)}"
            )
            
            # 如果有Milvus重试或错误，记录详细信息
            if current_file_stats.get('milvus_retry_count', 0) > 0:
                self.logger.warning(
                    f"文件 {orc_file_path} Milvus重试次数: {current_file_stats['milvus_retry_count']}, "
                    f"错误用户数: {current_file_stats.get('milvus_error_users', 0)}"
                )
            
            return user_data_list
            
        except Exception as e:
            self.logger.error(f"处理ORC文件失败: {orc_file_path}, {e}")
            raise

    def _process_orc_file_with_chunking(self, orc_file_path: str, process_date: str, prov_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        分批处理ORC文件（统一使用分批处理模式）

        Args:
            orc_file_path: ORC文件路径
            process_date: 处理日期
            prov_id: 省份ID

        Returns:
            处理后的用户数据列表
        """
        try:
            # 获取文件信息
            file_info = self.orc_reader.get_file_info(orc_file_path)
            if not file_info:
                self.logger.warning(f"无法获取文件信息: {orc_file_path}")
                return []

            # 文件基本信息
            file_size_mb = file_info.file_size / 1024 / 1024
            records_count = file_info.num_rows

            # 配置参数
            batch_size = getattr(self.config, 'batch_size', 1000)

            self.logger.info(f"分批处理文件: 大小={file_size_mb:.1f}MB, 记录数={records_count}, 用户批次大小={batch_size}")

            # 统一使用分批处理
            return self._process_file_chunked(orc_file_path, process_date, prov_id, batch_size)

        except Exception as e:
            self.logger.error(f"处理ORC文件失败: {orc_file_path}, {e}")
            raise

    def _process_file_chunked(self, orc_file_path: str, process_date: str, prov_id: Optional[str] = None,
                             batch_size: int = 1000) -> List[Dict[str, Any]]:
        """
        分批处理文件（按用户批次处理）

        Args:
            orc_file_path: ORC文件路径
            process_date: 处理日期
            prov_id: 省份ID
            batch_size: 用户批次大小

        Returns:
            处理后的用户数据列表
        """
        all_user_data_list = []

        # 读取整个ORC文件
        df = self._read_orc_file(orc_file_path)

        if df is None or df.empty:
            self.logger.warning(f"ORC文件为空或读取失败: {orc_file_path}")
            return []

        # 记录总记录数
        total_records = len(df)
        self.stats["total_records"] += total_records
        # 处理整个DataFrame，获取所有用户数据
        all_user_data_dict = self._process_dataframe(df, process_date, prov_id)

        if not all_user_data_dict:
            self.logger.warning("文件中没有有效的用户数据")
            return []

        total_users = len(all_user_data_dict)
        self.logger.info(f"开始处理: {total_records}条记录 → {total_users}个用户，批次大小={batch_size}")

        # 记录文件处理开始时间
        file_start_time = time.time()

        # 将用户数据按批次处理
        user_list = list(all_user_data_dict.items())
        batch_count = 0

        for i in range(0, total_users, batch_size):
            # 检查是否需要关闭服务
            if self.should_shutdown():
                self.logger.info("接收到关闭信号，停止分批处理")
                break

            batch_count += 1
            batch_users = user_list[i:i + batch_size]
            batch_user_dict = dict(batch_users)

            self.logger.info(f"处理用户批次 {batch_count}: {len(batch_user_dict)} 个用户 (第{i+1}-{min(i+batch_size, total_users)}个)")

            # 处理当前批次的用户
            processed_users = self._process_user_chunk(batch_user_dict, process_date)
            all_user_data_list.extend(processed_users)

            self.logger.info(f"批次 {batch_count} 处理完成: 有效用户数 {len(processed_users)}")

        self.logger.info(f"文件处理完成: 总批次数={batch_count}, 最终用户数={len(all_user_data_list)}")
        return all_user_data_list

    def process_orc_file_with_mongodb_write(self, orc_file_path: str, process_date: str, prov_id: Optional[str] = None, mongodb_ops=None) -> List[Dict[str, Any]]:
        """
        处理ORC文件并直接写入MongoDB（按批次处理）

        Args:
            orc_file_path: ORC文件路径
            process_date: 处理日期
            prov_id: 省份ID
            mongodb_ops: MongoDB操作对象

        Returns:
            所有处理的用户数据列表（用于统计）
        """
        try:
            self.logger.info(f"开始处理ORC文件: {orc_file_path}")

            # 设置当前处理的文件路径
            self.current_file_path = orc_file_path

            # 获取文件信息
            file_info = self.orc_reader.get_file_info(orc_file_path)
            if not file_info:
                self.logger.warning(f"无法获取文件信息: {orc_file_path}")
                return []

            # 文件基本信息
            file_size_mb = file_info.file_size / 1024 / 1024
            records_count = file_info.num_rows

            # 配置参数
            batch_size = getattr(self.config, 'batch_size', 1000)

            self.logger.info(f"分批处理文件: 大小={file_size_mb:.1f}MB, 记录数={records_count}, 用户批次大小={batch_size}")

            # 保存处理前的统计值（用于计算当前文件的统计）
            prev_mongodb_inserts = self.stats.get("mongodb_inserts", 0)
            prev_mongodb_updates = self.stats.get("mongodb_updates", 0)
            prev_skipped_users = self.stats.get("skipped_users", 0)

            # 读取整个ORC文件
            df = self._read_orc_file(orc_file_path)

            if df is None or df.empty:
                self.logger.warning(f"ORC文件为空或读取失败: {orc_file_path}")
                return []

            # 记录总记录数
            total_records = len(df)
            self.stats["total_records"] += total_records

            # 处理整个DataFrame，获取所有用户数据
            all_user_data_dict = self._process_dataframe(df, process_date, prov_id)

            if not all_user_data_dict:
                self.logger.warning("文件中没有有效的用户数据")
                return []

            total_users = len(all_user_data_dict)
            self.logger.info(f"文件中包含 {total_users} 个用户，开始分批处理和写入")

            # 记录文件处理开始时间
            file_start_time = time.time()

            # 分批处理并写入MongoDB
            all_processed_users = []
            user_list = list(all_user_data_dict.items())
            batch_count = 0

            for i in range(0, total_users, batch_size):
                # 检查是否需要关闭服务
                if self.should_shutdown():
                    self.logger.info("接收到关闭信号，停止分批处理")
                    break

                batch_count += 1
                batch_users = user_list[i:i + batch_size]
                batch_user_dict = dict(batch_users)

                # 处理当前批次的用户（PID去重、查询、截取）
                batch_start_time = time.time()
                processed_users = self._process_user_chunk(batch_user_dict, process_date)
                batch_process_time = time.time() - batch_start_time

                # MongoDB写入统计
                write_stats = {"inserted": 0, "updated": 0, "skipped": 0}

                if processed_users and mongodb_ops:
                    # 立即写入MongoDB
                    write_start_time = time.time()
                    write_result = self._write_batch_to_mongodb(processed_users, mongodb_ops)
                    write_time = time.time() - write_start_time

                    if not write_result["success"]:
                        self.logger.error(f"批次 {batch_count} 写入MongoDB失败")
                    else:
                        write_stats = write_result
                else:
                    write_time = 0

                all_processed_users.extend(processed_users)

                # 每10个批次输出一次进度（减少日志噪音）
                if batch_count % 10 == 0 or batch_count == 1:
                    total_batches = (total_users + batch_size - 1) // batch_size
                    progress = (batch_count / total_batches) * 100
                    avg_process_time = batch_process_time
                    avg_write_time = write_time
                    self.logger.info(f"批次进度 {batch_count}/{total_batches} ({progress:.1f}%) - "
                                   f"处理: {avg_process_time:.2f}s, 写入: {avg_write_time:.2f}s, "
                                   f"有效用户: {len(processed_users)}/{len(batch_user_dict)} | "
                                   f"💾 插入: {write_stats['inserted']}, 更新: {write_stats['updated']}, 跳过: {write_stats['skipped']}")

            # 计算处理统计
            total_process_time = time.time() - file_start_time
            valid_user_rate = len(all_processed_users) / total_users * 100 if total_users > 0 else 0
            processing_speed = total_records / total_process_time if total_process_time > 0 else 0

            # 累加有效用户数到全局统计
            current_file_effective_users = len(all_processed_users)
            self.stats["effective_users"] += current_file_effective_users

            # 计算当前文件的MongoDB操作统计（当前值减去处理前的值）
            current_file_inserts = self.stats.get("mongodb_inserts", 0) - prev_mongodb_inserts
            current_file_updates = self.stats.get("mongodb_updates", 0) - prev_mongodb_updates
            current_file_skipped = self.stats.get("skipped_users", 0) - prev_skipped_users

            self.logger.info(f"文件完成: {batch_count}批次, {len(all_processed_users)}/{total_users}用户({valid_user_rate:.1f}%), "
                           f"耗时{total_process_time:.1f}s, 速度{processing_speed:.0f}条/s | "
                           f"💾 本文件 插入: {current_file_inserts}, 更新: {current_file_updates}, 跳过: {current_file_skipped}")
            return all_processed_users

        except Exception as e:
            self.logger.error(f"处理ORC文件失败: {orc_file_path}, {e}")
            raise

    def _write_batch_to_mongodb(self, user_data_list: List[Dict[str, Any]], mongodb_ops) -> Dict[str, Any]:
        """
        写入批次数据到MongoDB

        Args:
            user_data_list: 用户数据列表
            mongodb_ops: MongoDB操作对象

        Returns:
            写入结果统计
        """
        try:
            if not user_data_list:
                return {"success": True, "inserted": 0, "updated": 0, "skipped": 0}

            # 先检查现有数据的时间戳，过滤掉不需要更新的数据
            uids = [user_data["_id"] for user_data in user_data_list]
            existing_data = {}

            try:
                # 查询现有用户的更新时间戳（使用_id查询）
                from shared.database.mongodb.operations import QueryOptions
                for existing_doc in mongodb_ops.find_many(
                    filter_dict={"_id": {"$in": uids}},
                    options=QueryOptions(projection={"_id": 1, "updated_days": 1})
                ):
                    existing_data[existing_doc["_id"]] = existing_doc.get("updated_days", 0)
            except:
                # 如果查询失败，继续处理所有数据
                pass

            # 过滤需要更新的数据
            valid_updates = []
            skipped_count = 0

            for user_data in user_data_list:
                uid = user_data["_id"]
                new_updated_days = user_data.get("updated_days", 0)
                existing_updated_days = existing_data.get(uid, 0)

                # 只有当新数据的时间戳大于现有数据时才更新
                if new_updated_days > existing_updated_days:
                    valid_updates.append(user_data)
                else:
                    skipped_count += 1

            if not valid_updates:
                return {"success": True, "inserted": 0, "updated": 0, "skipped": skipped_count}

            # 构建批量操作
            operations = []
            for user_data in valid_updates:
                # 准备更新数据，确保is_stored设置为false
                update_data = user_data.copy()
                if "vector_status" in update_data:
                    update_data["vector_status"]["is_stored"] = False
                    update_data["vector_status"]["stored_at_days"] = None
                else:
                    update_data["vector_status"] = {
                        "is_stored": False,
                        "stored_at_days": None
                    }

                # 构建智能更新操作（处理pid_groups覆盖逻辑）
                filter_dict = {"_id": user_data["_id"]}
                update_dict = self._build_smart_update_operation(update_data)

                from pymongo import UpdateOne
                operations.append(UpdateOne(filter_dict, update_dict, upsert=True))

            # 执行批量操作（使用配置的写入优化参数）
            result = mongodb_ops.bulk_write(operations)

            # 计算插入和更新数量
            inserted_count = result.inserted_count + result.upserted_count
            updated_count = result.modified_count

            # 更新统计信息
            self.stats["mongodb_inserts"] = self.stats.get("mongodb_inserts", 0) + inserted_count
            self.stats["mongodb_updates"] = self.stats.get("mongodb_updates", 0) + updated_count
            self.stats["skipped_users"] = self.stats.get("skipped_users", 0) + skipped_count

            return {
                "success": result.success,
                "inserted": inserted_count,
                "updated": updated_count,
                "skipped": skipped_count
            }

        except Exception as e:
            self.logger.error(f"写入MongoDB失败: {e}")
            return {"success": False, "inserted": 0, "updated": 0, "skipped": 0}

    def _apply_optimizations(self, user_data_dict: Dict[int, UserPIDData]) -> Dict[int, UserPIDData]:
        """
        应用数据优化处理（PID过滤和限制）

        Args:
            user_data_dict: 用户数据字典

        Returns:
            优化后的用户数据字典
        """
        if not user_data_dict:
            return user_data_dict

        self.logger.debug(f"开始数据优化处理，用户数: {len(user_data_dict)}")

        # 检查是否启用批量PID查询优化
        if getattr(self.config, 'enable_batch_optimization', True):
            # 步骤1：批量查询Milvus中存在的PID
            user_data_dict = self._batch_query_pids_for_users(user_data_dict)

            # 步骤2：批量限制PID数量（在Milvus查询之后）
            user_data_dict = self._batch_limit_pids_for_users(user_data_dict)
        else:
            self.logger.info("使用传统的逐用户处理方式")

            # 传统方式：逐个用户处理，但修复处理顺序
            for uid, user_data in user_data_dict.items():
                # 在处理每个用户前检查是否需要关闭服务
                if self.should_shutdown():
                    self.logger.info("接收到关闭信号，停止用户数据后处理")
                    break

                original_pid_count = len(user_data.pid_list)

                # 步骤1：先过滤Milvus中存在的PID
                user_data = self._filter_pids_by_milvus(user_data)
                after_milvus_count = len(user_data.pid_list)

                # 步骤2：再限制PID数量
                user_data = self._limit_pids_per_user(user_data)
                final_pid_count = len(user_data.pid_list)

                if original_pid_count != final_pid_count:
                    self.logger.debug(f"用户 {uid} PID变化: {original_pid_count} -> {after_milvus_count} -> {final_pid_count}")

                user_data_dict[uid] = user_data

        self.logger.debug(f"数据优化处理完成，最终用户数: {len(user_data_dict)}")
        return user_data_dict

    def _process_user_chunk(self, user_data_dict: Dict[int, UserPIDData], process_date: str) -> List[Dict[str, Any]]:
        """
        处理用户数据块

        Args:
            user_data_dict: 用户数据字典
            process_date: 处理日期

        Returns:
            处理后的用户数据列表
        """
        self.logger.debug(f"开始处理用户数据块，用户数: {len(user_data_dict)}")

        # 应用优化处理
        user_data_dict = self._apply_optimizations(user_data_dict)

        # 转换为MongoDB格式
        user_data_list = self._convert_to_mongodb_format(user_data_dict, process_date)

        self.logger.debug(f"用户数据块处理完成，有效用户数: {len(user_data_list)}")
        return user_data_list

    def _read_orc_file(self, orc_file_path: str) -> Optional[pd.DataFrame]:
        """
        读取ORC文件
        
        Args:
            orc_file_path: ORC文件路径
            
        Returns:
            DataFrame或None
        """
        try:
            # 设置读取选项
            read_options = ReadOptions(
                use_threads=True,
                batch_size=self.config.batch_size
            )
            
            # 读取ORC文件
            df = self.orc_reader.read_orc_file(orc_file_path, read_options)
            
            self.logger.debug(f"成功读取ORC文件: {orc_file_path}, 行数: {len(df)}")
            return df
            
        except Exception as e:
            self.logger.error(f"读取ORC文件失败: {orc_file_path}, {e}")
            return None
    
    def _process_dataframe(self, df: pd.DataFrame, process_date: str, prov_id: Optional[str] = None) -> Dict[int, UserPIDData]:
        """
        处理DataFrame数据
        
        Args:
            df: 输入DataFrame
            process_date: 处理日期
            prov_id: 省份ID（从文件路径提取）
            
        Returns:
            用户数据字典
        """
        user_data_dict = {}
        process_days = TimeUtils.date_to_days(process_date)
        
        # 逐行处理数据
        for index, row in df.iterrows():
            # 检查是否需要关闭服务
            if self.should_shutdown():
                self.logger.info("接收到关闭信号，停止数据处理")
                break
                
            try:
                # 提取和验证数据
                uid = self._extract_uid(row)
                pid_list = self._extract_pid_list(row)
                timestamp = self._extract_timestamp(row, process_days)
                provid = self._extract_provid(row) or prov_id

                if uid is None or not pid_list:
                    self.stats["invalid_records"] += 1
                    continue

                # 聚合用户数据
                if uid not in user_data_dict:
                    user_data_dict[uid] = UserPIDData(
                        uid=uid,
                        pid_list=[],
                        pid_timestamps={},
                        updated_days=process_days,
                        provid=provid
                    )

                user_data = user_data_dict[uid]

                # 添加PID列表中的每个PID（避免重复）
                for pid in pid_list:
                    if pid not in user_data.pid_timestamps:
                        user_data.pid_list.append(pid)
                        user_data.pid_timestamps[pid] = timestamp
                        self.stats["total_pids"] += 1

                self.stats["valid_records"] += 1

            except Exception as e:
                self.logger.warning(f"处理行数据失败: index={index}, error={e}")
                self.stats["invalid_records"] += 1
                continue
        
        # 返回原始用户数据，优化处理将在后续步骤中进行
        self.logger.debug(f"DataFrame处理完成，原始用户数: {len(user_data_dict)}")
        return user_data_dict
    
    def _extract_uid(self, row: pd.Series) -> Optional[int]:
        """
        提取和验证UID

        Args:
            row: 数据行

        Returns:
            UID或None
        """
        try:
            # 从配置中获取UID列名
            uid_columns = self.config.column_mapping.get('uid_columns', [])
            if not uid_columns:
                # 如果配置中没有指定，使用默认列名
                uid_columns = ['uid', 'user_id', 'userid', 'UID', 'USER_ID', 'id']

            for col in uid_columns:
                if col in row:
                    uid_value = row[col]
                    # 安全检查是否为空值
                    try:
                        if pd.isna(uid_value) or uid_value is None:
                            continue
                    except Exception:
                        continue
                    return self.data_processor.validate_and_convert_uid(uid_value)

            return None

        except Exception as e:
            self.logger.debug(f"提取UID失败: {e}")
            return None
    
    def _extract_pid_list(self, row: pd.Series) -> List[str]:
        """
        提取和验证PID列表

        Args:
            row: 数据行

        Returns:
            PID列表（可能为空）
        """
        try:
            # 从配置中获取PID列名
            pid_columns = self.config.column_mapping.get('pid_columns', [])
            if not pid_columns:
                # 如果配置中没有指定，使用默认列名
                pid_columns = ['pic_id_list', 'pid_list', 'pid', 'product_id', 'item_id', 'PID', 'PRODUCT_ID']

            for col in pid_columns:
                if col in row:
                    pid_value = row[col]

                    # 安全检查是否为空值，避免pandas数组的ambiguous truth value错误
                    try:
                        # 对于numpy数组或pandas Series，需要特殊处理
                        if hasattr(pid_value, '__len__') and not isinstance(pid_value, str):
                            # 如果是数组类型，检查是否为空
                            if len(pid_value) == 0:
                                continue
                        else:
                            # 对于标量值，检查是否为NaN或None
                            if pd.isna(pid_value) or pid_value is None:
                                continue
                    except Exception:
                        # 如果检查失败，跳过这个值
                        continue

                    # 处理不同类型的PID数据
                    if isinstance(pid_value, (list, tuple)):
                        # 如果是列表或元组，直接处理
                        return self._process_pid_list(pid_value)
                    elif isinstance(pid_value, str):
                        # 如果是字符串，尝试解析为列表
                        return self._parse_pid_string(pid_value)
                    else:
                        # 其他类型，尝试转换为字符串后处理
                        return self._parse_pid_string(str(pid_value))

            return []

        except Exception as e:
            self.logger.debug(f"提取PID列表失败: {e}")
            return []

    def _process_pid_list(self, pid_list: List[Any]) -> List[str]:
        """
        处理PID列表

        Args:
            pid_list: 原始PID列表

        Returns:
            验证后的PID列表
        """
        processed_pids = []

        for pid in pid_list:
            # 修复pandas数组处理问题：先检查是否为None，再安全检查NaN
            if pid is not None:
                try:
                    # 对于numpy数组或pandas Series，需要特殊处理
                    if hasattr(pid, '__len__') and not isinstance(pid, str):
                        # 如果是数组类型，跳过
                        continue

                    # 检查是否为NaN值
                    if pd.isna(pid):
                        continue

                    processed_pid = self.data_processor.validate_and_convert_pid(pid)
                    if processed_pid is not None:
                        processed_pids.append(processed_pid)
                except Exception as e:
                    # 记录详细错误信息但继续处理
                    self.logger.debug(f"处理PID失败: {pid}, 类型: {type(pid)}, 错误: {e}")
                    continue

        return processed_pids

    def _parse_pid_string(self, pid_string: str) -> List[str]:
        """
        解析PID字符串为列表

        Args:
            pid_string: PID字符串

        Returns:
            PID列表
        """
        try:
            # 预处理：移除可能的外层引号和方括号
            cleaned_string = pid_string.strip()
            
            # 移除外层引号
            if (cleaned_string.startswith("'") and cleaned_string.endswith("'")) or \
               (cleaned_string.startswith('"') and cleaned_string.endswith('"')):
                cleaned_string = cleaned_string[1:-1]
            
            # 移除外层方括号
            if cleaned_string.startswith('[') and cleaned_string.endswith(']'):
                cleaned_string = cleaned_string[1:-1]
            
            # 尝试不同的分隔符
            separators = [',', ';', '|', '\t', ' ']

            # 首先尝试JSON格式
            import json
            try:
                # 尝试解析为JSON数组
                if cleaned_string.startswith('[') or cleaned_string.startswith('"') or cleaned_string.startswith("'"):
                    parsed = json.loads(f'[{cleaned_string}]' if not cleaned_string.startswith('[') else cleaned_string)
                    if isinstance(parsed, list):
                        return self._process_pid_list(parsed)
            except (json.JSONDecodeError, ValueError):
                pass

            # 尝试分隔符分割
            for sep in separators:
                if sep in cleaned_string:
                    parts = []
                    for part in cleaned_string.split(sep):
                        part = part.strip()
                        # 移除每个部分的引号
                        part = part.strip("'\"")
                        if part:
                            parts.append(part)
                    if len(parts) > 1:
                        return self._process_pid_list(parts)

            # 如果没有分隔符，作为单个PID处理
            processed_pid = self.data_processor.validate_and_convert_pid(cleaned_string)
            return [processed_pid] if processed_pid is not None else []

        except Exception as e:
            self.logger.debug(f"解析PID字符串失败: {pid_string}, {e}")
            return []
    
    def _extract_timestamp(self, row: pd.Series, default_days: int) -> int:
        """
        提取时间戳
        
        Args:
            row: 数据行
            default_days: 默认天数
            
        Returns:
            时间戳（天数）
        """
        try:
            # 尝试从不同的列名提取时间戳
            timestamp_columns = ['timestamp', 'ts', 'time', 'event_time', 'created_time']
            
            for col in timestamp_columns:
                if col in row:
                    timestamp_value = row[col]
                    # 安全检查是否为空值
                    try:
                        if pd.isna(timestamp_value) or timestamp_value is None:
                            continue
                    except Exception:
                        continue

                    # 如果是天数格式
                    if isinstance(timestamp_value, int) and timestamp_value > 0:
                        return timestamp_value

                    # 如果是日期字符串
                    try:
                        return TimeUtils.date_to_days(timestamp_value)
                    except:
                        continue
            
            # 使用默认天数
            return default_days
            
        except Exception as e:
            self.logger.debug(f"提取时间戳失败: {e}")
            return default_days
    
    def _extract_provid(self, row: pd.Series) -> Optional[str]:
        """
        提取provid
        
        Args:
            row: 数据行
            
        Returns:
            provid或None
        """
        try:
            # 尝试从不同的列名提取provid
            provid_columns = ['provid', 'provider_id', 'prov_id', 'PROVID']
            
            for col in provid_columns:
                if col in row:
                    provid_value = row[col]
                    # 安全检查是否为空值
                    try:
                        if pd.isna(provid_value) or provid_value is None:
                            continue
                    except Exception:
                        continue
                    return str(provid_value).strip()
            
            return None
            
        except Exception as e:
            self.logger.debug(f"提取provid失败: {e}")
            return None
    
    def _batch_limit_pids_for_users(self, user_data_dict: Dict[int, UserPIDData]) -> Dict[int, UserPIDData]:
        """
        批量限制多个用户的PID数量（在Milvus查询之后）

        Args:
            user_data_dict: 用户数据字典

        Returns:
            限制PID数量后的用户数据字典
        """
        updated_user_data_dict = {}

        for uid, user_data in user_data_dict.items():
            # 检查是否需要关闭服务
            if self.should_shutdown():
                self.logger.info("接收到关闭信号，停止PID数量限制")
                break

            # 限制单个用户的PID数量
            limited_user_data = self._limit_pids_per_user(user_data)
            updated_user_data_dict[uid] = limited_user_data

        return updated_user_data_dict

    def _limit_pids_per_user(self, user_data: UserPIDData) -> UserPIDData:
        """
        限制每个用户的PID数量
        
        Args:
            user_data: 用户数据
            
        Returns:
            处理后的用户数据
        """
        max_pids = self.config.max_pids_per_user
        
        if len(user_data.pid_list) <= max_pids:
            return user_data
        
        # 按时间戳排序，保留最新的PIDs
        pid_timestamp_pairs = [
            (pid, user_data.pid_timestamps.get(pid, 0))
            for pid in user_data.pid_list
        ]
        
        # 按时间戳降序排序
        pid_timestamp_pairs.sort(key=lambda x: x[1], reverse=True)
        
        # 保留最新的PIDs
        latest_pids = [pid for pid, _ in pid_timestamp_pairs[:max_pids]]
        latest_timestamps = {
            pid: user_data.pid_timestamps[pid] 
            for pid in latest_pids
        }
        
        user_data.pid_list = latest_pids
        user_data.pid_timestamps = latest_timestamps
        
        return user_data

    def _filter_pids_by_milvus(self, user_data: UserPIDData) -> UserPIDData:
        """
        过滤Milvus中存在的PID

        Args:
            user_data: 用户数据

        Returns:
            过滤后的用户数据
        """
        if not user_data.pid_list:
            return user_data

        # 检查是否启用Milvus过滤
        if not getattr(self.config, 'enable_milvus_filtering', True):
            self.logger.debug("Milvus过滤已禁用，保留所有PID")
            return user_data

        # 查询Milvus中存在的PID
        valid_pids = self._query_pids_in_milvus(user_data.pid_list)

        # 如果Milvus不可用，_query_pids_in_milvus会返回原列表
        # 如果Milvus可用但没有找到任何PID，则清空列表
        if not valid_pids and self.content_ops:
            # 只有在Milvus可用但确实没有找到PID时才清空
            self.logger.debug(f"用户 {user_data.uid} 的所有PID在Milvus中都不存在")
            user_data.pid_list = []
            user_data.pid_timestamps = {}
            return user_data
        elif not valid_pids:
            # Milvus不可用，保留原数据
            return user_data

        # 过滤PID列表和时间戳
        filtered_timestamps = {
            pid: user_data.pid_timestamps[pid]
            for pid in valid_pids
            if pid in user_data.pid_timestamps
        }

        user_data.pid_list = valid_pids
        user_data.pid_timestamps = filtered_timestamps

        return user_data

    def _convert_to_mongodb_format(self, user_data_dict: Dict[int, UserPIDData],
                                  process_date: str) -> List[Dict[str, Any]]:
        """
        转换为MongoDB格式
        
        Args:
            user_data_dict: 用户数据字典
            process_date: 处理日期
            
        Returns:
            MongoDB格式的用户数据列表
        """
        result = []
        process_days = TimeUtils.date_to_days(process_date)
        invalid_user_count = 0

        for uid, user_data in user_data_dict.items():
            # 跳过没有有效PID的用户，并统计无效用户数
            if not user_data.pid_list:
                invalid_user_count += 1
                continue

            # 按时间戳分组PID，构建pid_groups结构
            pid_groups = self._build_pid_groups(user_data)

            # 构建MongoDB文档（按照指定顺序）
            # 使用有序字典确保字段顺序：_id, pid_groups, pid_count, created_days, updated_days, vector_status, prov_id
            # 注意：使用uid作为_id，移除原有的uid字段
            from collections import OrderedDict
            document = OrderedDict([
                ("_id", uid),  # 使用uid作为_id主键
                ("pid_groups", pid_groups),
                ("pid_count", len(user_data.pid_list)),
                ("created_days", process_days),
                ("updated_days", process_days),
                ("vector_status", OrderedDict([
                    ("is_stored", False),
                    ("stored_at_days", None)
                ])),
                ("prov_id", str(user_data.provid) if user_data.provid else "100")  # 默认为"100"
            ])

            result.append(document)

        # 更新无效用户统计
        self.stats["invalid_users"] += invalid_user_count

        return result

    def _build_pid_groups(self, user_data: UserPIDData) -> List[Dict[str, Any]]:
        """
        构建pid_groups结构

        Args:
            user_data: 用户数据

        Returns:
            pid_groups列表
        """
        # 按时间戳分组PID
        grouped_pids = self._group_pids_by_timestamp(user_data)

        # 构建pid_groups结构
        pid_groups = []
        for timestamp_days, pids in grouped_pids.items():
            if pids:  # 只添加非空的PID组
                pid_groups.append({
                    "timestamp_days": timestamp_days,
                    "pids": pids
                })

        # 按时间戳排序
        pid_groups.sort(key=lambda x: x["timestamp_days"])

        return pid_groups

    def _group_pids_by_timestamp(self, user_data: UserPIDData) -> Dict[int, List[str]]:
        """
        按时间戳分组PID
        
        Args:
            user_data: 用户数据
            
        Returns:
            按时间戳分组的PID字典
        """
        grouped = defaultdict(list)
        
        for pid in user_data.pid_list:
            timestamp = user_data.pid_timestamps.get(pid, user_data.updated_days)
            grouped[timestamp].append(pid)
        
        # 去重每个时间戳下的PID
        for timestamp in grouped:
            grouped[timestamp] = list(set(grouped[timestamp]))
        
        return dict(grouped)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取处理统计"""
        return self.stats.copy()

    def _build_smart_update_operation(self, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        构建智能更新操作，处理pid_groups的覆盖逻辑

        当存在相同timestamp_days时，新的pid_groups会覆盖旧的pid_groups

        Args:
            update_data: 要更新的数据

        Returns:
            MongoDB更新操作字典
        """
        # 如果没有pid_groups，使用简单的$set操作
        if "pid_groups" not in update_data:
            return {"$set": update_data}

        new_pid_groups = update_data["pid_groups"]

        # 构建更新操作
        update_operations = {}

        # 处理pid_groups的覆盖逻辑
        if new_pid_groups:
            # 创建timestamp_days到pid_groups的映射
            timestamp_to_groups = {}
            for group in new_pid_groups:
                timestamp_days = group.get("timestamp_days")
                if timestamp_days is not None:
                    timestamp_to_groups[timestamp_days] = group

            # 构建更新操作：先删除相同timestamp_days的组，然后添加新的组
            if timestamp_to_groups:
                # 删除具有相同timestamp_days的现有组
                update_operations["$pull"] = {
                    "pid_groups": {
                        "timestamp_days": {"$in": list(timestamp_to_groups.keys())}
                    }
                }

                # 添加新的pid_groups
                update_operations["$push"] = {
                    "pid_groups": {"$each": new_pid_groups}
                }

        # 处理其他字段的更新
        other_fields = {k: v for k, v in update_data.items() if k != "pid_groups"}
        if other_fields:
            if "$set" not in update_operations:
                update_operations["$set"] = {}
            update_operations["$set"].update(other_fields)

        # 如果没有构建任何操作，回退到简单的$set
        if not update_operations:
            return {"$set": update_data}

        return update_operations
